import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';
import RecipeCacheService from './RecipeCacheService';

// Google Custom Search API configuration
const GOOGLE_SEARCH_API_KEY = 'AIzaSyB3bzVudVDXpKaZHCeMOOhpGQRe54YFyr0'; // Using same Gemini API key
const GOOGLE_SEARCH_ENGINE_ID = '07071c91da38d45f4';
const GOOGLE_SEARCH_API_URL = 'https://www.googleapis.com/customsearch/v1';

// Permanent image cache interface for storing Unsplash images (NEVER expires)
interface PermanentImageCache {
  [mealName: string]: {
    imageUrl: string;
    cachedAt: number;
    // No expiration - images are cached permanently
  };
}

export interface MealData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  imageUrl: string;
  description?: string;
}

export interface WeekPlan {
  week: Array<{
    day: string;
    meals: { [key: string]: MealData };
  }>;
  weekNumber: number;
  year: number;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  isActive: boolean;
  generatedAt: number; // timestamp
}

export interface WeeklyPlanConfig {
  autoGenerate: boolean;
  generateDaysAhead: number; // How many days before week ends to generate next week
  maxStoredWeeks: number; // Maximum number of weeks to keep in storage
}

class WeeklyPlanManager {
  private static instance: WeeklyPlanManager;
  private readonly WEEKLY_PLANS_KEY = 'weekly_plans_storage';
  private readonly CURRENT_WEEK_KEY = 'current_week_info';
  private readonly CONFIG_KEY = 'weekly_plan_config';
  private readonly IMAGE_CACHE_KEY = 'permanent_meal_image_cache';
  // No expiry - images are cached permanently to avoid repeated API calls

  static getInstance(): WeeklyPlanManager {
    if (!WeeklyPlanManager.instance) {
      WeeklyPlanManager.instance = new WeeklyPlanManager();
    }
    return WeeklyPlanManager.instance;
  }

  // Get current week information using proper ISO week calculation
  getCurrentWeekInfo(): { weekNumber: number; year: number; startDate: Date; endDate: Date } {
    const now = new Date();

    // Calculate start of current week (Monday) - ISO week starts on Monday
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const startDate = new Date(now);
    startDate.setDate(now.getDate() + daysToMonday);
    startDate.setHours(0, 0, 0, 0);

    // Calculate end of current week (Sunday)
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    endDate.setHours(23, 59, 59, 999);

    // Proper ISO week number calculation
    const weekNumber = this.getISOWeekNumber(startDate);
    const weekYear = this.getISOWeekYear(startDate);

    console.log(`📅 Week calculation: Week ${weekNumber} of ${weekYear}`);
    console.log(`📅 Week period: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    return {
      weekNumber,
      year: weekYear,
      startDate,
      endDate
    };
  }

  // Calculate ISO week number (1-53) - handles year boundaries correctly
  private getISOWeekNumber(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7; // Monday = 0, Sunday = 6
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    const firstThursday = tempDate.getTime();
    tempDate.setMonth(0, 1); // January 1st
    if (tempDate.getDay() !== 4) {
      tempDate.setMonth(0, 1 + ((4 - tempDate.getDay()) + 7) % 7);
    }
    return 1 + Math.ceil((firstThursday - tempDate.getTime()) / 604800000); // 604800000 = 7 * 24 * 3600 * 1000
  }

  // Get the year for ISO week (can differ from calendar year)
  private getISOWeekYear(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7;
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    return tempDate.getFullYear();
  }

  // Enhance meal plan with FULL AI-GENERATED data (no static content!)
  private async enhanceMealPlanWithData(basicWeek: Array<{day: string; meals: {[key: string]: string}}>): Promise<Array<{day: string; meals: {[key: string]: MealData}}>> {
    console.log('🤖 Enhancing meal plan with FULL AI-GENERATED data (no static content)...');
    const startTime = Date.now();

    const enhancedWeek = [];

    for (const day of basicWeek) {
      console.log(`📅 Processing ${day.day} with AI-generated content...`);

      const enhancedDay = {
        day: day.day,
        meals: {} as {[key: string]: MealData}
      };

      // Process meals with FULL AI-GENERATED data - generate complete recipes
      for (const [mealType, mealName] of Object.entries(day.meals)) {
        try {
          console.log(`🤖 Generating AI content for: ${mealName} (${mealType})`);
          // Generate COMPLETE meal data with full AI recipe generation
          enhancedDay.meals[mealType] = await this.generateCompleteMealData(mealName, mealType);
        } catch (error) {
          console.error(`❌ Error generating AI content for ${mealName}:`, error);
          // Even fallback should be sophisticated, not static
          enhancedDay.meals[mealType] = await this.generateSophisticatedFallback(mealName, mealType);
        }
      }

      enhancedWeek.push(enhancedDay);
    }

    const endTime = Date.now();
    console.log(`✅ Enhanced meal plan with FULL AI content in ${endTime - startTime}ms`);
    return enhancedWeek;
  }

  // Generate sophisticated fallback using RecipeCacheService (still AI-powered!)
  private async generateSophisticatedFallback(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🔄 Generating sophisticated fallback for: ${mealName}`);

      // Use RecipeCacheService which has sophisticated fallbacks
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.error(`❌ Even sophisticated fallback failed for ${mealName}:`, error);
      // Last resort - but still better than basic static data
      return await this.getFallbackMealData(mealName, mealType);
    }
  }

  // Basic fallback meal data (last resort only)
  private async getFallbackMealData(mealName: string, mealType: string): Promise<MealData> {
    const imageUrl = await this.getFallbackImage(mealName);

    return {
      name: mealName,
      calories: this.estimateCalories(mealType),
      protein: Math.round(this.estimateCalories(mealType) * 0.25 / 4),
      carbs: Math.round(this.estimateCalories(mealType) * 0.45 / 4),
      fat: Math.round(this.estimateCalories(mealType) * 0.30 / 9),
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // Generate FULL AI meal data (ALWAYS generate real content!)
  private async generateBasicMealData(mealName: string, mealType: string): Promise<MealData> {
    console.log(`🤖 Generating FULL AI data for: ${mealName} (${mealType}) - REAL AI generation`);

    try {
      // ALWAYS generate or get AI content - no static data!
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.warn(`⚠️ Error generating AI data for ${mealName}:`, error);
      // Even fallback should try to be AI-powered
      return this.generateSophisticatedFallback(mealName, mealType);
    }
  }

  // Generate complete meal data for a single meal using RecipeCacheService (ONLY used for on-demand generation)
  private async generateCompleteMealData(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🍽️ Generating COMPLETE data for: ${mealName} (${mealType}) - FULL recipe generation`);

      // Use RecipeCacheService to get or generate recipe
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.warn(`⚠️ Failed to generate complete data for ${mealName}, using estimates:`, error);
      return await this.getEstimatedMealData(mealName, mealType);
    }
  }

  // Get estimated meal data with dynamic Unsplash image
  private async getEstimatedMealData(mealName: string, mealType: string): Promise<MealData> {
    const calories = this.estimateCalories(mealType);
    const imageUrl = await this.getFallbackImage(mealName);

    return {
      name: mealName,
      calories: calories,
      protein: Math.round(calories * 0.25 / 4),
      carbs: Math.round(calories * 0.45 / 4),
      fat: Math.round(calories * 0.30 / 9),
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // Estimate calories based on meal type
  private estimateCalories(mealType: string): number {
    const calorieMap: {[key: string]: number} = {
      breakfast: 400,
      lunch: 500,
      dinner: 600,
      snack: 200,
      snack1: 150,
      snack2: 150
    };
    return calorieMap[mealType.toLowerCase()] || 400;
  }

  // Get cached image or fetch new one from Google Custom Search (PERMANENT CACHING - never expires)
  private async getFallbackImage(mealName: string): Promise<string> {
    // First check if we have a permanently cached image for this meal
    const cachedImage = await this.getCachedImage(mealName);
    if (cachedImage) {
      console.log(`📖 Using PERMANENTLY cached image for ${mealName} - NO API CALL NEEDED: ${cachedImage}`);
      return cachedImage;
    }

    // If not cached, fetch from Google Custom Search and cache it
    const searchTerm = mealName.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();

    try {
      console.log(`🖼️ WeeklyPlanManager: Making NEW Google Custom Search API call for: ${mealName} (will be cached PERMANENTLY)`);

      // Primary search with exact meal name
      const primaryUrl = `${GOOGLE_SEARCH_API_URL}?key=${GOOGLE_SEARCH_API_KEY}&cx=${GOOGLE_SEARCH_ENGINE_ID}&q=${encodeURIComponent(searchTerm)}&searchType=image&num=1&safe=active&imgType=photo&imgSize=medium`;

      const response = await fetch(primaryUrl);

      if (response.ok) {
        const data = await response.json();

        if (data.items && data.items.length > 0) {
          const imageUrl = data.items[0].link;
          console.log(`✅ WeeklyPlanManager: Found Google Custom Search image for ${mealName}: ${imageUrl}`);

          // Cache the image for future use
          await this.cacheImage(mealName, imageUrl);
          return imageUrl;
        } else {
          console.log(`⚠️ WeeklyPlanManager: No specific results for ${mealName}, trying generic food search`);

          // Fallback to generic food search
          const genericUrl = `${GOOGLE_SEARCH_API_URL}?key=${GOOGLE_SEARCH_API_KEY}&cx=${GOOGLE_SEARCH_ENGINE_ID}&q=food&searchType=image&num=5&safe=active&imgType=photo&imgSize=medium`;
          const genericResponse = await fetch(genericUrl);

          if (genericResponse.ok) {
            const genericData = await genericResponse.json();
            if (genericData.items && genericData.items.length > 0) {
              const imageUrl = genericData.items[0].link;
              console.log(`✅ WeeklyPlanManager: Using generic food image for ${mealName}: ${imageUrl}`);

              // Cache the generic image
              await this.cacheImage(mealName, imageUrl);
              return imageUrl;
            }
          }

          throw new Error('No search results found');
        }
      } else {
        console.error('❌ WeeklyPlanManager: Google Custom Search API error:', response.status, response.statusText);

        // Try to get response text for more details
        try {
          const errorText = await response.text();
          console.error('❌ WeeklyPlanManager: Google Custom Search error details:', errorText);
        } catch (e) {
          console.error('❌ WeeklyPlanManager: Could not read error response');
        }

        throw new Error(`Google Custom Search API error: ${response.status}`);
      }
    } catch (error) {
      console.warn(`⚠️ WeeklyPlanManager: Failed to fetch Google Custom Search image for ${mealName}:`, error);

      // Return high-quality static fallback image based on meal type
      const searchLower = mealName.toLowerCase();
      let fallbackImage: string;

      if (searchLower.includes('chicken')) {
        fallbackImage = 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3';
      } else if (searchLower.includes('salmon') || searchLower.includes('fish')) {
        fallbackImage = 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3';
      } else if (searchLower.includes('salad')) {
        fallbackImage = 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3';
      } else if (searchLower.includes('pasta')) {
        fallbackImage = 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3';
      } else {
        // Default high-quality food image
        fallbackImage = 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
      }

      // Cache the fallback image too
      await this.cacheImage(mealName, fallbackImage);
      return fallbackImage;
    }
  }

  // Get permanently cached image for a meal (NEVER expires)
  private async getCachedImage(mealName: string): Promise<string | null> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return null;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      const cachedEntry = cache[normalizedMealName];

      if (!cachedEntry) return null;

      // Images NEVER expire - return cached image immediately
      console.log(`📖 Found permanently cached image for ${mealName} (cached ${new Date(cachedEntry.cachedAt).toLocaleDateString()})`);
      return cachedEntry.imageUrl;
    } catch (error) {
      console.error('❌ Error getting cached image:', error);
      return null;
    }
  }

  // Cache an image for a meal PERMANENTLY (NEVER expires)
  private async cacheImage(mealName: string, imageUrl: string): Promise<void> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      const cache: PermanentImageCache = cacheJson ? JSON.parse(cacheJson) : {};

      const normalizedMealName = mealName.toLowerCase().trim();
      const now = Date.now();

      // Only cache if not already cached (avoid overwriting existing entries)
      if (!cache[normalizedMealName]) {
        cache[normalizedMealName] = {
          imageUrl,
          cachedAt: now
          // No expiration - cached permanently
        };

        await AsyncStorage.setItem(this.IMAGE_CACHE_KEY, JSON.stringify(cache));
        console.log(`💾 PERMANENTLY cached image for ${mealName} - will NEVER make API call again for this meal`);
      } else {
        console.log(`📖 Image already permanently cached for ${mealName} - skipping cache update`);
      }
    } catch (error) {
      console.error('❌ Error caching image:', error);
    }
  }

  // Get cache statistics (for debugging) - no expiration cleanup needed
  async getImageCacheStats(): Promise<{ totalImages: number; cacheSize: string; oldestCacheDate: string }> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const totalImages = Object.keys(cache).length;
      const cacheSize = `${Math.round(cacheJson.length / 1024)} KB`;

      // Find oldest cached image
      const oldestEntry = Object.values(cache).reduce((oldest, current) =>
        current.cachedAt < oldest.cachedAt ? current : oldest
      );
      const oldestCacheDate = oldestEntry ? new Date(oldestEntry.cachedAt).toLocaleDateString() : 'N/A';

      return { totalImages, cacheSize, oldestCacheDate };
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };
    }
  }

  // Clear ALL cached images (only if user explicitly wants to reset)
  async clearAllImageCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.IMAGE_CACHE_KEY);
      console.log(`🧹 Cleared ALL permanently cached images - API calls will be made again for all meals`);
    } catch (error) {
      console.error('❌ Error clearing all image cache:', error);
    }
  }

  // Check if an image is already permanently cached (for debugging/optimization)
  async isImageCached(mealName: string): Promise<boolean> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return false;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      return !!cache[normalizedMealName];
    } catch (error) {
      console.error('❌ Error checking if image is cached:', error);
      return false;
    }
  }

  // Replace a specific meal in the current week plan (for alternatives)
  async replaceMealInCurrentPlan(dayName: string, mealType: string, userProfile: any): Promise<MealData | null> {
    try {
      console.log(`🔄 Generating alternative meal for ${dayName} ${mealType}`);

      // Get current plan
      const currentPlan = await this.getActiveWeeklyPlan();
      if (!currentPlan) {
        throw new Error('No active plan found');
      }

      // Find the current meal to get context for better alternatives
      const dayIndex = currentPlan.week.findIndex(day => day.day.toLowerCase() === dayName.toLowerCase());
      if (dayIndex === -1) {
        throw new Error(`Day ${dayName} not found in current plan`);
      }

      const currentMeal = currentPlan.week[dayIndex].meals[mealType];
      if (!currentMeal) {
        throw new Error(`Meal type ${mealType} not found for ${dayName}`);
      }

      // Build goal for alternative meal generation
      const goalComponents = [];
      goalComponents.push(`Generate a different ${mealType} meal to replace "${currentMeal.name}"`);

      if (userProfile.dietaryRestrictions?.length > 0) {
        goalComponents.push(`Dietary restrictions: ${userProfile.dietaryRestrictions.join(', ')}`);
      }
      if (userProfile.allergies?.length > 0) {
        goalComponents.push(`Avoid allergens: ${userProfile.allergies.join(', ')}`);
      }
      if (userProfile.preferredCuisines?.length > 0) {
        goalComponents.push(`Preferred cuisines: ${userProfile.preferredCuisines.join(', ')}`);
      }

      const goal = goalComponents.join('. ');

      // Generate new meal name
      const newMealName = await ApiService.generateSingleMeal(goal);

      // Generate complete meal data using RecipeCacheService
      const newMealData = await this.generateCompleteMealData(newMealName, mealType);

      // Update ONLY the specific meal in the current plan
      currentPlan.week[dayIndex].meals[mealType] = newMealData;

      // Use targeted update method instead of full plan storage
      await this.updateSpecificMealInStorage(currentPlan, dayIndex, mealType, newMealData);

      console.log(`✅ Replaced ${dayName} ${mealType}: "${currentMeal.name}" → "${newMealName}"`);
      return newMealData;

    } catch (error) {
      console.error('❌ Error replacing meal:', error);
      return null;
    }
  }

  // Targeted method to update only a specific meal without affecting the rest of the plan
  private async updateSpecificMealInStorage(plan: WeekPlan, dayIndex: number, mealType: string, newMealData: MealData): Promise<void> {
    try {
      // Get existing plans from storage
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];

      // Find and update the current active plan
      const activePlanIndex = existingPlans.findIndex(p =>
        p.isActive &&
        p.weekNumber === plan.weekNumber &&
        p.year === plan.year
      );

      if (activePlanIndex !== -1) {
        // Update only the specific meal
        existingPlans[activePlanIndex].week[dayIndex].meals[mealType] = newMealData;

        // Save back to storage
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(existingPlans));

        console.log(`✅ Updated specific meal in storage: ${plan.week[dayIndex].day} ${mealType}`);
      } else {
        console.warn('⚠️ Active plan not found in storage, falling back to full plan update');
        await this.storeWeeklyPlan(plan);
      }

    } catch (error) {
      console.error('❌ Error updating specific meal in storage:', error);
      // Fallback to full plan update
      await this.storeWeeklyPlan(plan);
    }
  }

  // Check if current stored plan is still valid for this week
  async isCurrentPlanValid(): Promise<boolean> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const storedWeekInfo = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);
      
      if (!storedWeekInfo) {
        console.log('📅 No stored week info found');
        return false;
      }
      
      const stored = JSON.parse(storedWeekInfo);
      const isValid = stored.weekNumber === currentWeekInfo.weekNumber && 
                     stored.year === currentWeekInfo.year;
      
      console.log(`📅 Week plan validity check: ${isValid ? 'VALID' : 'EXPIRED'}`);
      console.log(`📅 Current: Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log(`📅 Stored: Week ${stored.weekNumber}, ${stored.year}`);
      
      return isValid;
    } catch (error) {
      console.error('❌ Error checking plan validity:', error);
      return false;
    }
  }

  // Generate new weekly plan for current week
  async generateWeeklyPlan(userProfile: any): Promise<WeekPlan | null> {
    const startTime = performance.now();
    console.log('🚀 PERFORMANCE: Starting weekly plan generation...');

    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      console.log(`📅 Generating new weekly plan for Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log('👤 User profile data:', JSON.stringify({
        dietaryRestrictions: userProfile.dietaryRestrictions,
        caloriesGoal: userProfile.caloriesGoal,
        allergies: userProfile.allergies,
        preferredCuisines: userProfile.preferredCuisines,
        activityLevel: userProfile.activityLevel,
        healthGoals: userProfile.healthGoals
      }, null, 2));

      // Map user profile to API format with proper field names
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000, // Handle both field names
        mealsPerDay: 3, // Default to 3 meals per day
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: userProfile.healthGoals || []
      };

      console.log('🔄 Mapped API options:', JSON.stringify(apiOptions, null, 2));

      // Generate meal plan using the corrected API method
      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (!mealPlanResult || !mealPlanResult.week) {
        throw new Error('Failed to generate meal plan from API');
      }

      // Enhance meal plan with complete meal data (nutrition + images)
      const enhancedWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

      const weekPlan: WeekPlan = {
        week: enhancedWeek,
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };

      // Store the new plan
      await this.storeWeeklyPlan(weekPlan);
      await this.updateCurrentWeekInfo(currentWeekInfo);

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`🚀 PERFORMANCE: Weekly plan generation completed in ${totalTime.toFixed(2)}ms`);
      console.log(`✅ Generated and stored weekly plan for Week ${currentWeekInfo.weekNumber}`);

      // Performance metrics
      const mealsCount = enhancedWeek.reduce((total, day) => total + Object.keys(day.meals).length, 0);
      console.log(`📊 PERFORMANCE METRICS:
        - Total time: ${totalTime.toFixed(2)}ms
        - Meals processed: ${mealsCount}
        - Average time per meal: ${(totalTime / mealsCount).toFixed(2)}ms
        - Performance mode: OPTIMIZED (basic data only)`);

      return weekPlan;

    } catch (error) {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.error(`❌ Error generating weekly plan in ${totalTime.toFixed(2)}ms:`, error);
      console.error('❌ Full error details:', error);
      return null;
    }
  }

  // Store weekly plan in AsyncStorage
  async storeWeeklyPlan(weekPlan: WeekPlan): Promise<void> {
    try {
      // Get existing plans
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];

      // Find if this plan already exists (for updates)
      const existingPlanIndex = existingPlans.findIndex(plan =>
        plan.weekNumber === weekPlan.weekNumber &&
        plan.year === weekPlan.year
      );

      if (existingPlanIndex !== -1) {
        // Update existing plan (preserve its active status unless explicitly setting a new active plan)
        existingPlans[existingPlanIndex] = {
          ...existingPlans[existingPlanIndex],
          ...weekPlan,
          // Only change active status if the new plan is explicitly marked as active
          isActive: weekPlan.isActive !== undefined ? weekPlan.isActive : existingPlans[existingPlanIndex].isActive
        };
      } else {
        // New plan - only mark others as inactive if this new plan is active
        if (weekPlan.isActive) {
          existingPlans.forEach(plan => {
            if (plan.isActive) {
              plan.isActive = false;
            }
          });
        }
        existingPlans.push(weekPlan);
      }

      // Keep only recent weeks (cleanup old plans)
      const config = await this.getConfig();
      const sortedPlans = existingPlans.sort((a, b) => b.generatedAt - a.generatedAt);
      const plansToKeep = sortedPlans.slice(0, config.maxStoredWeeks);

      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plansToKeep));

      // Also store in database for backup (if available)
      try {
        await DatabaseIntegrationService.saveWeeklyPlanToDatabase(weekPlan);
      } catch (error) {
        console.warn('⚠️ Could not save to database, continuing with AsyncStorage only:', error);
      }

    } catch (error) {
      console.error('❌ Error storing weekly plan:', error);
      throw error;
    }
  }

  // Update current week info
  private async updateCurrentWeekInfo(weekInfo: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CURRENT_WEEK_KEY, JSON.stringify(weekInfo));
    } catch (error) {
      console.error('❌ Error updating current week info:', error);
      throw error;
    }
  }

  // Get active weekly plan
  async getActiveWeeklyPlan(): Promise<WeekPlan | null> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return null;
      
      const plans: WeekPlan[] = JSON.parse(plansJson);
      const activePlan = plans.find(plan => plan.isActive);
      
      return activePlan || null;
    } catch (error) {
      console.error('❌ Error getting active weekly plan:', error);
      return null;
    }
  }

  // Get configuration
  private async getConfig(): Promise<WeeklyPlanConfig> {
    try {
      const configJson = await AsyncStorage.getItem(this.CONFIG_KEY);
      if (configJson) {
        return JSON.parse(configJson);
      }
      
      // Default configuration
      const defaultConfig: WeeklyPlanConfig = {
        autoGenerate: true,
        generateDaysAhead: 2, // Generate new plan 2 days before current week ends
        maxStoredWeeks: 8 // Keep 8 weeks of history
      };
      
      await AsyncStorage.setItem(this.CONFIG_KEY, JSON.stringify(defaultConfig));
      return defaultConfig;
    } catch (error) {
      console.error('❌ Error getting config:', error);
      return {
        autoGenerate: true,
        generateDaysAhead: 2,
        maxStoredWeeks: 8
      };
    }
  }

  // Check if we need to generate next week's plan
  async shouldGenerateNextWeek(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      if (!config.autoGenerate) return false;
      
      const currentWeekInfo = this.getCurrentWeekInfo();
      const now = new Date();
      const daysUntilWeekEnd = Math.ceil((currentWeekInfo.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      console.log(`📅 Days until week end: ${daysUntilWeekEnd}`);
      console.log(`📅 Generate days ahead setting: ${config.generateDaysAhead}`);
      
      return daysUntilWeekEnd <= config.generateDaysAhead;
    } catch (error) {
      console.error('❌ Error checking if should generate next week:', error);
      return false;
    }
  }

  // Main function to ensure current week has a valid plan
  async ensureCurrentWeekPlan(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('📅 Ensuring current week has valid plan...');
      
      // Check if current plan is valid
      const isValid = await this.isCurrentPlanValid();
      
      if (isValid) {
        console.log('📅 Current plan is valid, returning existing plan');
        return await this.getActiveWeeklyPlan();
      }
      
      console.log('📅 Current plan is invalid or missing, generating new plan');
      return await this.generateWeeklyPlan(userProfile);
      
    } catch (error) {
      console.error('❌ Error ensuring current week plan:', error);
      return null;
    }
  }

  // Get plan history for analytics
  async getPlanHistory(limit: number = 4): Promise<WeekPlan[]> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return [];

      const plans: WeekPlan[] = JSON.parse(plansJson);
      return plans
        .sort((a, b) => b.generatedAt - a.generatedAt)
        .slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting plan history:', error);
      return [];
    }
  }

  // Comprehensive cleanup method to handle year transitions and old data
  async performMaintenanceCleanup(): Promise<void> {
    try {
      console.log('🧹 Performing weekly plan maintenance cleanup...');

      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return;

      const plans: WeekPlan[] = JSON.parse(plansJson);
      const currentWeekInfo = this.getCurrentWeekInfo();
      const currentTime = Date.now();
      const oneYearAgo = currentTime - (365 * 24 * 60 * 60 * 1000);

      // Filter out plans older than 1 year and inactive plans older than 8 weeks
      const filteredPlans = plans.filter(plan => {
        const planAge = currentTime - plan.generatedAt;
        const eightWeeksAgo = currentTime - (8 * 7 * 24 * 60 * 60 * 1000);

        // Keep if less than 1 year old
        if (plan.generatedAt > oneYearAgo) {
          // If inactive and older than 8 weeks, remove
          if (!plan.isActive && plan.generatedAt < eightWeeksAgo) {
            return false;
          }
          return true;
        }
        return false;
      });

      // Ensure current week has an active plan
      const hasCurrentWeekPlan = filteredPlans.some(plan =>
        plan.weekNumber === currentWeekInfo.weekNumber &&
        plan.year === currentWeekInfo.year &&
        plan.isActive
      );

      if (!hasCurrentWeekPlan) {
        console.log('⚠️ No active plan for current week found during cleanup');
      }

      // Save cleaned plans
      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(filteredPlans));

      console.log(`✅ Cleanup complete: Kept ${filteredPlans.length} plans, removed ${plans.length - filteredPlans.length} old plans`);

    } catch (error) {
      console.error('❌ Error during maintenance cleanup:', error);
    }
  }

  // Initialize weekly plan system - call this on app startup
  async initializeWeeklyPlanSystem(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚀 Initializing weekly plan system...');

      // Perform maintenance cleanup first
      await this.performMaintenanceCleanup();

      // Ensure current week has a valid plan
      const currentPlan = await this.ensureCurrentWeekPlan(userProfile);

      // Check if we should pre-generate next week
      const shouldGenerateNext = await this.shouldGenerateNextWeek();
      if (shouldGenerateNext && userProfile) {
        console.log('📅 Pre-generating next week\'s plan in background...');
        // Don't await this - let it run in background
        this.generateNextWeekPlan(userProfile).catch(error => {
          console.error('❌ Error pre-generating next week:', error);
        });
      }

      console.log('✅ Weekly plan system initialized');
      return currentPlan;

    } catch (error) {
      console.error('❌ Error initializing weekly plan system:', error);
      return null;
    }
  }

  // Generate next week's plan in advance
  private async generateNextWeekPlan(userProfile: any): Promise<void> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const nextWeekInfo = {
        weekNumber: currentWeekInfo.weekNumber === 53 ? 1 : currentWeekInfo.weekNumber + 1,
        year: currentWeekInfo.weekNumber === 53 ? currentWeekInfo.year + 1 : currentWeekInfo.year,
        startDate: new Date(currentWeekInfo.startDate.getTime() + (7 * 24 * 60 * 60 * 1000)),
        endDate: new Date(currentWeekInfo.endDate.getTime() + (7 * 24 * 60 * 60 * 1000))
      };

      // Check if next week already has a plan
      const existingPlans = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (existingPlans) {
        const plans: WeekPlan[] = JSON.parse(existingPlans);
        const hasNextWeekPlan = plans.some(plan =>
          plan.weekNumber === nextWeekInfo.weekNumber &&
          plan.year === nextWeekInfo.year
        );

        if (hasNextWeekPlan) {
          console.log('📅 Next week already has a plan, skipping generation');
          return;
        }
      }

      console.log(`📅 Generating plan for next week: Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);

      // Generate the plan for next week
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000,
        mealsPerDay: 3,
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: userProfile.healthGoals || []
      };

      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (mealPlanResult && mealPlanResult.week) {
        // Enhance next week's plan with complete data
        const enhancedNextWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

        const nextWeekPlan: WeekPlan = {
          week: enhancedNextWeek,
          weekNumber: nextWeekInfo.weekNumber,
          year: nextWeekInfo.year,
          startDate: nextWeekInfo.startDate.toISOString(),
          endDate: nextWeekInfo.endDate.toISOString(),
          isActive: false, // Not active until next week starts
          generatedAt: Date.now()
        };

        // Store the next week's plan
        const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
        const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];
        existingPlans.push(nextWeekPlan);

        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(existingPlans));

        console.log(`✅ Pre-generated plan for Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);
      }

    } catch (error) {
      console.error('❌ Error generating next week plan:', error);
    }
  }

  // Clear all cached plans and force regeneration with fresh AI content
  async clearAllPlans(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Also clear recipe cache to ensure fresh AI generation
      await RecipeCacheService.clearAllCache();

      console.log('🧹 All weekly plans and recipe cache cleared - fresh AI content will be generated');
    } catch (error) {
      console.error('❌ Error clearing weekly plans:', error);
    }
  }

  // Force regenerate current week with fresh AI content
  async forceRegenerateCurrentWeek(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🔄 Force regenerating current week with fresh AI content...');

      // Clear current week data
      const currentWeekInfo = this.getCurrentWeekInfo();
      await this.deactivateWeek(currentWeekInfo.weekNumber, currentWeekInfo.year);

      // Clear related recipe cache
      await RecipeCacheService.clearAllCache();

      // Generate fresh plan
      return await this.generateWeeklyPlan(userProfile);
    } catch (error) {
      console.error('❌ Error force regenerating current week:', error);
      return null;
    }
  }
}

export default WeeklyPlanManager.getInstance();
